#!/usr/bin/env python3
"""
Test script for JustDial D2C Brand Scraper
==========================================

This script tests the basic functionality of the scraper to ensure everything is working correctly.
"""

import sys
import logging
from justdial_d2c_scraper import JustDialD2CScraper

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import selenium
        print(f"✓ Selenium: {selenium.__version__}")
    except ImportError as e:
        print(f"✗ Selenium import failed: {e}")
        return False
    
    try:
        import pandas
        print(f"✓ Pandas: {pandas.__version__}")
    except ImportError as e:
        print(f"✗ Pandas import failed: {e}")
        return False
    
    try:
        import requests
        print(f"✓ Requests: {requests.__version__}")
    except ImportError as e:
        print(f"✗ Requests import failed: {e}")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✓ BeautifulSoup4: Available")
    except ImportError as e:
        print(f"✗ BeautifulSoup4 import failed: {e}")
        return False
    
    try:
        import openpyxl
        print(f"✓ OpenPyXL: {openpyxl.__version__}")
    except ImportError as e:
        print(f"✗ OpenPyXL import failed: {e}")
        return False
    
    return True

def test_webdriver():
    """Test if WebDriver can be initialized"""
    print("\nTesting WebDriver initialization...")
    
    try:
        scraper = JustDialD2CScraper(headless=True)
        scraper.setup_driver()
        
        if scraper.driver:
            print("✓ WebDriver initialized successfully")
            
            # Test basic navigation
            scraper.driver.get("https://www.google.com")
            title = scraper.driver.title
            print(f"✓ Navigation test successful (Page title: {title[:30]}...)")
            
            scraper.driver.quit()
            return True
        else:
            print("✗ WebDriver initialization failed")
            return False
            
    except Exception as e:
        print(f"✗ WebDriver test failed: {e}")
        return False

def test_phone_decoder():
    """Test phone number decoding functionality"""
    print("\nTesting phone number decoder...")
    
    scraper = JustDialD2CScraper()
    
    # Test cases for JustDial's phone encoding
    test_cases = [
        ("mobilesv-yxwvutsrqponmlkji", "1234567890"),
        ("mobilesv-dcyzwxvutsrqponmlkji", "+1234567890"),
        ("mobilesv-fehgyzwxvutsrqponmlkji", "(1234567890"),
    ]
    
    success_count = 0
    for encoded, expected in test_cases:
        try:
            decoded = scraper.decode_phone_number(encoded)
            if decoded == expected:
                print(f"✓ Decoded '{encoded}' -> '{decoded}'")
                success_count += 1
            else:
                print(f"✗ Decoded '{encoded}' -> '{decoded}' (expected '{expected}')")
        except Exception as e:
            print(f"✗ Error decoding '{encoded}': {e}")
    
    return success_count == len(test_cases)

def test_business_validation():
    """Test business validation logic"""
    print("\nTesting business validation...")
    
    scraper = JustDialD2CScraper()
    
    # Test valid business
    valid_business = {
        'Company Name': 'Artisan Crafts Studio',
        'Address': '123 CG Road, Ahmedabad, Gujarat',
        'Phone Number': '9876543210',
        'Products': 'Handmade pottery and crafts'
    }
    
    if scraper.is_valid_d2c_business(valid_business):
        print("✓ Valid business correctly identified")
    else:
        print("✗ Valid business incorrectly rejected")
        return False
    
    # Test invalid business (IT service)
    invalid_business = {
        'Company Name': 'Tech Solutions IT Services',
        'Address': '456 SG Highway, Ahmedabad, Gujarat',
        'Phone Number': '9876543211',
        'Products': 'Software development and IT consulting'
    }
    
    if not scraper.is_valid_d2c_business(invalid_business):
        print("✓ Invalid business correctly rejected")
    else:
        print("✗ Invalid business incorrectly accepted")
        return False
    
    # Test business without Ahmedabad address
    non_ahmedabad_business = {
        'Company Name': 'Mumbai Crafts',
        'Address': '789 Mumbai Road, Mumbai, Maharashtra',
        'Phone Number': '9876543212',
        'Products': 'Handmade crafts'
    }
    
    if not scraper.is_valid_d2c_business(non_ahmedabad_business):
        print("✓ Non-Ahmedabad business correctly rejected")
    else:
        print("✗ Non-Ahmedabad business incorrectly accepted")
        return False
    
    return True

def test_category_mapping():
    """Test category mapping functionality"""
    print("\nTesting category mapping...")
    
    scraper = JustDialD2CScraper()
    
    test_cases = [
        ("Handmade Pottery Studio", "Handicrafts"),
        ("Fashion Designer Boutique", "Apparel"),
        ("Home Decor Solutions", "Home Decor"),
        ("Custom Gift Shop", "Gifts"),
        ("Tech Startup", "Other"),
    ]
    
    success_count = 0
    for company_name, expected_category in test_cases:
        business_info = {
            'Company Name': company_name,
            'Products': '',
            'Category': ''
        }
        
        # Simulate category detection
        text_content = f"{business_info['Company Name']} {business_info['Products']}".lower()
        for keyword, category in scraper.categories.items():
            if keyword in text_content:
                business_info['Category'] = category
                break
        
        if not business_info['Category']:
            business_info['Category'] = 'Other'
        
        if business_info['Category'] == expected_category:
            print(f"✓ '{company_name}' -> '{business_info['Category']}'")
            success_count += 1
        else:
            print(f"✗ '{company_name}' -> '{business_info['Category']}' (expected '{expected_category}')")
    
    return success_count == len(test_cases)

def test_data_saving():
    """Test data saving functionality"""
    print("\nTesting data saving...")
    
    scraper = JustDialD2CScraper()
    
    # Create sample data
    scraper.scraped_data = [
        {
            'Company Name': 'Test Artisan Studio',
            'Phone Number': '9876543210',
            'Email': '<EMAIL>',
            'Website / Link': 'https://test.com',
            'Contact Person': 'John Doe',
            'Address': '123 Test Road, Ahmedabad',
            'Category': 'Handicrafts',
            'Products': 'Handmade pottery',
            'Search Keyword': 'test keyword',
            'Scraped Date': '2025-01-20 12:00:00'
        }
    ]
    
    try:
        # Test CSV saving
        scraper.save_to_csv("test_output.csv")
        print("✓ CSV saving successful")
        
        # Test Excel saving
        scraper.save_to_excel("test_output.xlsx")
        print("✓ Excel saving successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Data saving failed: {e}")
        return False

def run_all_tests():
    """Run all tests and return overall result"""
    print("JustDial D2C Brand Scraper - Test Suite")
    print("=" * 50)
    
    # Suppress logging during tests
    logging.getLogger().setLevel(logging.ERROR)
    
    tests = [
        ("Import Test", test_imports),
        ("WebDriver Test", test_webdriver),
        ("Phone Decoder Test", test_phone_decoder),
        ("Business Validation Test", test_business_validation),
        ("Category Mapping Test", test_category_mapping),
        ("Data Saving Test", test_data_saving),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The scraper is ready to use.")
        return True
    else:
        print("❌ Some tests failed. Please check the installation and dependencies.")
        return False

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        # Quick test - only imports and basic functionality
        print("Running quick tests...")
        success = test_imports() and test_business_validation()
        if success:
            print("✓ Quick tests passed!")
        else:
            print("✗ Quick tests failed!")
    else:
        # Full test suite
        run_all_tests()

if __name__ == "__main__":
    main()
