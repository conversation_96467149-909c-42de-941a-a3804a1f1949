@echo off
REM JustDial D2C Brand Scraper - Windows Batch Script
REM ================================================

echo JustDial D2C Brand Scraper for Ahmedabad
echo ==========================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

echo Python is installed
echo.

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo Error: Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Dependencies installed successfully!
echo.

REM Run tests first
echo Running tests to verify installation...
python test_scraper.py --quick
if errorlevel 1 (
    echo.
    echo Warning: Some tests failed. You may encounter issues.
    echo Do you want to continue anyway? (y/n)
    set /p continue=
    if /i not "%continue%"=="y" (
        echo Scraping cancelled.
        pause
        exit /b 1
    )
)

echo.
echo Starting scraper...
echo.

REM Run the main scraper
python justdial_d2c_scraper.py

echo.
echo Scraping completed!
echo Check the generated CSV and Excel files for results.
echo.

pause
