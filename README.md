# JustDial D2C Brand Scraper for Ahmedabad

A comprehensive Python script to scrape direct-to-consumer (D2C) homegrown small and medium-scale brands based in Ahmedabad from JustDial website. This tool focuses on brands that design, create, or sell handcrafted, innovative, or unique products.

## Features

- **Comprehensive Keyword Search**: Uses 30+ targeted keywords covering handcrafted, fashion, home decor, gifts, and startup categories
- **Smart Data Extraction**: Extracts company name, phone, email, website, address, category, and product information
- **Phone Number Decoding**: Automatically decodes JustDial's encoded phone numbers
- **Duplicate Removal**: Intelligent duplicate detection based on company name and phone number
- **Multiple Output Formats**: Saves data in both CSV and Excel formats
- **Filtering**: Excludes IT services, trading companies, and non-local businesses
- **Logging**: Comprehensive logging for monitoring scraping progress
- **Rate Limiting**: Built-in delays to avoid being blocked

## Required Data Fields

The scraper extracts the following information for each business:

| Field | Description |
|-------|-------------|
| Company Name | Registered or brand name |
| Phone Number | Primary contact number (decoded from JustDial) |
| Email | Contact email (when available) |
| Website / Link | Official website or online storefront |
| Contact Person | Founder / Owner / Key stakeholder |
| Address | Physical location / studio / store address |
| Category | E.g., Handicrafts, Apparel, Decor, Gifts, Art, etc. |
| Products | Types of products or offerings (brief description) |

## Installation

1. **Clone or download the repository**
   ```bash
   git clone <repository-url>
   cd insta-scrapper
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Chrome WebDriver**
   The script uses `webdriver-manager` to automatically download and manage ChromeDriver, so no manual installation is needed.

## Usage

### Basic Usage

Run the scraper with default settings:

```bash
python justdial_d2c_scraper.py
```

### Advanced Usage

You can customize the scraper by modifying the configuration in `config.py` or by creating a custom script:

```python
from justdial_d2c_scraper import JustDialD2CScraper

# Initialize scraper
scraper = JustDialD2CScraper(
    headless=True,  # Set to False to see browser
    delay_range=(2, 5)  # Random delay between requests
)

# Run scraping
scraper.scrape_all_keywords(max_pages_per_keyword=3)

# Remove duplicates
scraper.remove_duplicates()

# Save results
scraper.save_to_csv("my_results.csv")
scraper.save_to_excel("my_results.xlsx")
```

## Configuration

Edit `config.py` to customize:

- **Scraping settings**: Number of pages, delays, timeouts
- **Browser settings**: Headless mode, window size, user agent
- **Output settings**: File names, formatting options
- **Filtering settings**: Exclusion keywords, validation rules
- **Additional keywords**: Add more search terms

## Keywords Used

The scraper searches for businesses using these keyword categories:

### 🔨 Handcrafted / Handmade / Artisanal
- handmade products ahmedabad
- artisan brand ahmedabad
- craft studio ahmedabad
- pottery ahmedabad
- ceramic studio ahmedabad

### 👗 Apparel / Accessories / Fashion
- fashion startup ahmedabad
- slow fashion ahmedabad
- designer label ahmedabad
- handcrafted jewelry ahmedabad
- sustainable clothing ahmedabad

### 🏠 Home Decor / Lifestyle
- homegrown decor ahmedabad
- lifestyle brand ahmedabad
- handmade decor ahmedabad
- candle brand ahmedabad
- eco-friendly brand ahmedabad

### 🎁 Gifts & Local Brands
- local products ahmedabad
- curated gifts ahmedabad
- personalized gifts ahmedabad
- artisanal brand ahmedabad

### 🚀 Startup / Small Business
- d2c brand ahmedabad
- startup brand ahmedabad
- emerging brand ahmedabad

## Output Files

The scraper generates:

1. **CSV file**: `ahmedabad_d2c_brands_YYYYMMDD_HHMMSS.csv`
2. **Excel file**: `ahmedabad_d2c_brands_YYYYMMDD_HHMMSS.xlsx`
3. **Log file**: `justdial_scraper.log`

## Filtering Criteria

The scraper automatically filters out:

- **Excluded business types**: IT services, marketing agencies, traders, resellers
- **Non-local businesses**: Companies not based in Ahmedabad
- **Incomplete listings**: Businesses without basic contact information

## Sample Output

```csv
Company Name,Phone Number,Email,Website / Link,Contact Person,Address,Category,Products
"Artisan Crafts Studio",+919876543210,<EMAIL>,https://artisancrafts.com,,123 CG Road Ahmedabad,Handicrafts,"Handmade pottery and ceramic items"
"Eco Fashion House",+919876543211,<EMAIL>,,,456 SG Highway Ahmedabad,Apparel,"Sustainable clothing and accessories"
```

## Troubleshooting

### Common Issues

1. **ChromeDriver not found**
   - The script automatically downloads ChromeDriver, but ensure you have Chrome browser installed

2. **No data scraped**
   - Check your internet connection
   - Verify that JustDial is accessible
   - Try reducing the number of pages per keyword

3. **Getting blocked**
   - Increase delay ranges in config.py
   - Reduce the number of concurrent requests
   - Use a VPN if necessary

### Debugging

Enable debug logging by modifying `config.py`:

```python
LOGGING_CONFIG = {
    'level': 'DEBUG',
    # ... other settings
}
```

## Legal and Ethical Considerations

- **Respect robots.txt**: Check JustDial's robots.txt file
- **Rate limiting**: The script includes delays to avoid overwhelming servers
- **Data usage**: Use scraped data responsibly and in compliance with applicable laws
- **Terms of service**: Ensure compliance with JustDial's terms of service

## Contributing

Feel free to contribute by:
- Adding more relevant keywords
- Improving data extraction accuracy
- Adding new output formats
- Enhancing filtering logic

## License

This project is for educational and research purposes. Please ensure compliance with applicable laws and website terms of service.

## Support

For issues or questions:
1. Check the log file for error details
2. Review the troubleshooting section
3. Ensure all dependencies are properly installed
4. Verify your Python version (3.7+ recommended)
