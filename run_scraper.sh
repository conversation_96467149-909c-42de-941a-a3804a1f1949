#!/bin/bash
# JustDial D2C Brand Scraper - Linux/Mac Shell Script
# ===================================================

echo "JustDial D2C Brand Scraper for Ahmedabad"
echo "========================================"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.7+ and try again"
    exit 1
fi

echo "Python 3 is installed"
echo

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "Error: Failed to create virtual environment"
        exit 1
    fi
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "Error: Failed to install dependencies"
    exit 1
fi

echo
echo "Dependencies installed successfully!"
echo

# Run tests first
echo "Running tests to verify installation..."
python test_scraper.py --quick
if [ $? -ne 0 ]; then
    echo
    echo "Warning: Some tests failed. You may encounter issues."
    read -p "Do you want to continue anyway? (y/n): " continue
    if [ "$continue" != "y" ] && [ "$continue" != "Y" ]; then
        echo "Scraping cancelled."
        exit 1
    fi
fi

echo
echo "Starting scraper..."
echo

# Run the main scraper
python justdial_d2c_scraper.py

echo
echo "Scraping completed!"
echo "Check the generated CSV and Excel files for results."
echo

# Deactivate virtual environment
deactivate
