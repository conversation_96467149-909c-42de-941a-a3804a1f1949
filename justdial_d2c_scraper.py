#!/usr/bin/env python3
"""
JustDial D2C Brand Scraper for Ahmedabad
========================================

This script scrapes direct-to-consumer (D2C) homegrown small and medium-scale brands 
based in Ahmedabad from JustDial website. It focuses on brands that design, create 
or sell handcrafted, innovative, or unique products.

Author: AI Assistant
Date: 2025-01-20
"""

import csv
import time
import random
import logging
import requests
from datetime import datetime
from urllib.parse import quote_plus, urljoin
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup
import pandas as pd

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('justdial_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class JustDialD2CScraper:
    """
    A comprehensive scraper for extracting D2C brand information from JustDial
    """
    
    def __init__(self, headless=True, delay_range=(2, 5)):
        """
        Initialize the scraper with configuration options
        
        Args:
            headless (bool): Run browser in headless mode
            delay_range (tuple): Range for random delays between requests
        """
        self.headless = headless
        self.delay_range = delay_range
        self.driver = None
        self.scraped_data = []
        
        # Keywords for D2C brand search in Ahmedabad
        self.keywords = [
            # Handcrafted / Handmade / Artisanal
            "handmade products ahmedabad",
            "artisan brand ahmedabad", 
            "craft studio ahmedabad",
            "handcrafted gifts ahmedabad",
            "makers ahmedabad",
            "creative studio ahmedabad",
            "pottery ahmedabad",
            "ceramic studio ahmedabad",
            "design studio ahmedabad",
            
            # Apparel / Accessories / Fashion
            "fashion startup ahmedabad",
            "slow fashion ahmedabad",
            "designer label ahmedabad",
            "handcrafted jewelry ahmedabad",
            "sustainable clothing ahmedabad",
            "boutique clothing ahmedabad",
            "ethnic wear ahmedabad",
            
            # Home Decor / Lifestyle
            "homegrown decor ahmedabad",
            "lifestyle brand ahmedabad",
            "handmade decor ahmedabad",
            "home decor studio ahmedabad",
            "candle brand ahmedabad",
            "eco-friendly brand ahmedabad",
            "macrame ahmedabad",
            "handmade furniture ahmedabad",
            
            # Gifts & Local Brands
            "local products ahmedabad",
            "curated gifts ahmedabad",
            "gifting solutions ahmedabad",
            "personalized gifts ahmedabad",
            "artisanal brand ahmedabad",
            "indie brand ahmedabad",
            
            # Startup / Small Business Terminology
            "d2c brand ahmedabad",
            "startup brand ahmedabad",
            "small business ahmedabad handmade",
            "upcoming brands ahmedabad",
            "local start-up ahmedabad",
            "emerging brand ahmedabad"
        ]
        
        # Categories mapping
        self.categories = {
            'handmade': 'Handicrafts',
            'artisan': 'Handicrafts', 
            'craft': 'Handicrafts',
            'pottery': 'Handicrafts',
            'ceramic': 'Handicrafts',
            'fashion': 'Apparel',
            'clothing': 'Apparel',
            'jewelry': 'Accessories',
            'ethnic': 'Apparel',
            'decor': 'Home Decor',
            'furniture': 'Home Decor',
            'candle': 'Home Decor',
            'lifestyle': 'Lifestyle',
            'gifts': 'Gifts',
            'gifting': 'Gifts',
            'personalized': 'Gifts'
        }
    
    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options"""
        try:
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument("--headless")
            
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            logger.info("Chrome WebDriver initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {str(e)}")
            raise
    
    def random_delay(self):
        """Add random delay to avoid being blocked"""
        delay = random.uniform(*self.delay_range)
        time.sleep(delay)
    
    def decode_phone_number(self, encoded_class):
        """
        Decode JustDial's encoded phone numbers
        Based on their encoding scheme
        """
        try:
            # JustDial's phone number encoding mapping
            decode_map = {
                'acb': '0', 'yz': '1', 'wx': '2', 'vu': '3', 'ts': '4',
                'rq': '5', 'po': '6', 'nm': '7', 'lk': '8', 'ji': '9',
                'dc': '+', 'fe': '(', 'hg': ')', 'ba': '-'
            }
            
            # Extract the encoded part from class name
            if 'mobilesv' in encoded_class:
                encoded_part = encoded_class.split('-')[-1] if '-' in encoded_class else ''
                decoded = ''.join([decode_map.get(encoded_part[i:i+2], '') 
                                 for i in range(0, len(encoded_part), 2)])
                return decoded
            return ""
            
        except Exception as e:
            logger.warning(f"Failed to decode phone number: {str(e)}")
            return ""
    
    def extract_business_info(self, business_element):
        """
        Extract detailed business information from a JustDial listing element
        
        Args:
            business_element: Selenium WebElement containing business listing
            
        Returns:
            dict: Extracted business information
        """
        business_info = {
            'Company Name': '',
            'Phone Number': '',
            'Email': '',
            'Website / Link': '',
            'Contact Person': '',
            'Address': '',
            'Category': '',
            'Products': ''
        }
        
        try:
            # Extract company name
            try:
                name_element = business_element.find_element(By.CLASS_NAME, "lng_cont_name")
                business_info['Company Name'] = name_element.text.strip()
            except NoSuchElementException:
                try:
                    name_element = business_element.find_element(By.CSS_SELECTOR, ".fn.org")
                    business_info['Company Name'] = name_element.text.strip()
                except NoSuchElementException:
                    pass
            
            # Extract address
            try:
                address_element = business_element.find_element(By.CLASS_NAME, "cont_sw_addr")
                business_info['Address'] = address_element.text.strip()
            except NoSuchElementException:
                try:
                    address_element = business_element.find_element(By.CSS_SELECTOR, ".adr")
                    business_info['Address'] = address_element.text.strip()
                except NoSuchElementException:
                    pass
            
            # Extract phone number (JustDial uses encoded phone numbers)
            try:
                phone_elements = business_element.find_elements(By.CSS_SELECTOR, "[class*='mobilesv']")
                if phone_elements:
                    for phone_elem in phone_elements:
                        class_name = phone_elem.get_attribute('class')
                        decoded_phone = self.decode_phone_number(class_name)
                        if decoded_phone and len(decoded_phone) >= 10:
                            business_info['Phone Number'] = decoded_phone
                            break
            except Exception as e:
                logger.warning(f"Failed to extract phone number: {str(e)}")
            
            # Extract website/link
            try:
                website_elements = business_element.find_elements(By.CSS_SELECTOR, "a[href*='http']")
                for link in website_elements:
                    href = link.get_attribute('href')
                    if href and 'justdial.com' not in href:
                        business_info['Website / Link'] = href
                        break
            except Exception:
                pass
            
            # Extract email (if available)
            try:
                email_elements = business_element.find_elements(By.CSS_SELECTOR, "[href*='mailto']")
                if email_elements:
                    email_href = email_elements[0].get_attribute('href')
                    business_info['Email'] = email_href.replace('mailto:', '')
            except Exception:
                pass
            
            # Extract products/services description
            try:
                desc_element = business_element.find_element(By.CLASS_NAME, "lng_cont_addr")
                business_info['Products'] = desc_element.text.strip()
            except NoSuchElementException:
                try:
                    desc_element = business_element.find_element(By.CSS_SELECTOR, ".mrehover")
                    business_info['Products'] = desc_element.text.strip()
                except NoSuchElementException:
                    pass
            
            # Determine category based on keywords
            text_content = f"{business_info['Company Name']} {business_info['Products']}".lower()
            for keyword, category in self.categories.items():
                if keyword in text_content:
                    business_info['Category'] = category
                    break
            
            if not business_info['Category']:
                business_info['Category'] = 'Other'
            
        except Exception as e:
            logger.error(f"Error extracting business info: {str(e)}")
        
        return business_info

    def scrape_keyword(self, keyword, max_pages=3):
        """
        Scrape businesses for a specific keyword

        Args:
            keyword (str): Search keyword
            max_pages (int): Maximum number of pages to scrape

        Returns:
            list: List of business information dictionaries
        """
        businesses = []
        logger.info(f"Starting scrape for keyword: {keyword}")

        try:
            # Construct JustDial search URL
            base_url = "https://www.justdial.com/Ahmedabad"
            search_query = quote_plus(keyword)
            search_url = f"{base_url}/{search_query}"

            for page in range(1, max_pages + 1):
                logger.info(f"Scraping page {page} for keyword: {keyword}")

                # Navigate to search page
                if page == 1:
                    url = search_url
                else:
                    url = f"{search_url}/page-{page}"

                self.driver.get(url)
                self.random_delay()

                # Wait for page to load
                try:
                    WebDriverWait(self.driver, 15).until(
                        EC.presence_of_element_located((By.CLASS_NAME, "store-details"))
                    )
                except TimeoutException:
                    logger.warning(f"Timeout waiting for page to load: {url}")
                    continue

                # Find all business listings
                business_elements = self.driver.find_elements(By.CLASS_NAME, "store-details")

                if not business_elements:
                    logger.warning(f"No business listings found on page {page}")
                    break

                # Extract information from each business
                for element in business_elements:
                    try:
                        business_info = self.extract_business_info(element)

                        # Filter out businesses that don't meet D2C criteria
                        if self.is_valid_d2c_business(business_info):
                            business_info['Search Keyword'] = keyword
                            business_info['Scraped Date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            businesses.append(business_info)
                            logger.info(f"Added business: {business_info['Company Name']}")

                    except Exception as e:
                        logger.error(f"Error processing business element: {str(e)}")
                        continue

                self.random_delay()

                # Check if there are more pages
                try:
                    next_button = self.driver.find_element(By.CSS_SELECTOR, ".next")
                    if "disabled" in next_button.get_attribute("class"):
                        logger.info(f"Reached last page for keyword: {keyword}")
                        break
                except NoSuchElementException:
                    logger.info(f"No more pages for keyword: {keyword}")
                    break

        except Exception as e:
            logger.error(f"Error scraping keyword '{keyword}': {str(e)}")

        logger.info(f"Completed scraping for keyword: {keyword}. Found {len(businesses)} businesses")
        return businesses

    def is_valid_d2c_business(self, business_info):
        """
        Check if a business meets D2C criteria

        Args:
            business_info (dict): Business information

        Returns:
            bool: True if business meets D2C criteria
        """
        # Basic validation
        if not business_info.get('Company Name') or not business_info.get('Address'):
            return False

        # Check if address contains Ahmedabad
        address = business_info.get('Address', '').lower()
        if 'ahmedabad' not in address:
            return False

        # Exclude certain business types
        exclude_keywords = [
            'it service', 'software', 'digital marketing', 'seo', 'web development',
            'trading', 'wholesale', 'distributor', 'dealer', 'agent', 'broker',
            'consultant', 'consultancy', 'agency', 'advertising'
        ]

        business_text = f"{business_info.get('Company Name', '')} {business_info.get('Products', '')}".lower()

        for exclude_word in exclude_keywords:
            if exclude_word in business_text:
                return False

        return True

    def scrape_all_keywords(self, max_pages_per_keyword=3):
        """
        Scrape all keywords and compile results

        Args:
            max_pages_per_keyword (int): Maximum pages to scrape per keyword
        """
        logger.info("Starting comprehensive D2C brand scraping")

        try:
            self.setup_driver()

            for i, keyword in enumerate(self.keywords, 1):
                logger.info(f"Processing keyword {i}/{len(self.keywords)}: {keyword}")

                try:
                    businesses = self.scrape_keyword(keyword, max_pages_per_keyword)
                    self.scraped_data.extend(businesses)

                    # Add longer delay between keywords to avoid blocking
                    if i < len(self.keywords):
                        delay = random.uniform(5, 10)
                        logger.info(f"Waiting {delay:.1f} seconds before next keyword...")
                        time.sleep(delay)

                except Exception as e:
                    logger.error(f"Error processing keyword '{keyword}': {str(e)}")
                    continue

            logger.info(f"Scraping completed. Total businesses found: {len(self.scraped_data)}")

        except Exception as e:
            logger.error(f"Error in scrape_all_keywords: {str(e)}")
        finally:
            if self.driver:
                self.driver.quit()

    def remove_duplicates(self):
        """Remove duplicate businesses based on company name and phone number"""
        seen = set()
        unique_data = []

        for business in self.scraped_data:
            # Create identifier based on company name and phone
            identifier = f"{business.get('Company Name', '').lower()}_{business.get('Phone Number', '')}"

            if identifier not in seen and business.get('Company Name'):
                seen.add(identifier)
                unique_data.append(business)

        original_count = len(self.scraped_data)
        self.scraped_data = unique_data
        removed_count = original_count - len(unique_data)

        logger.info(f"Removed {removed_count} duplicate entries. {len(unique_data)} unique businesses remain.")

    def save_to_csv(self, filename=None):
        """
        Save scraped data to CSV file

        Args:
            filename (str): Output filename (optional)
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"ahmedabad_d2c_brands_{timestamp}.csv"

        if not self.scraped_data:
            logger.warning("No data to save")
            return

        try:
            # Define CSV columns
            fieldnames = [
                'Company Name', 'Phone Number', 'Email', 'Website / Link',
                'Contact Person', 'Address', 'Category', 'Products',
                'Search Keyword', 'Scraped Date'
            ]

            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.scraped_data)

            logger.info(f"Data saved to {filename}")
            logger.info(f"Total records: {len(self.scraped_data)}")

            # Print summary statistics
            self.print_summary()

        except Exception as e:
            logger.error(f"Error saving to CSV: {str(e)}")

    def save_to_excel(self, filename=None):
        """
        Save scraped data to Excel file with formatting

        Args:
            filename (str): Output filename (optional)
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"ahmedabad_d2c_brands_{timestamp}.xlsx"

        if not self.scraped_data:
            logger.warning("No data to save")
            return

        try:
            df = pd.DataFrame(self.scraped_data)

            # Reorder columns
            column_order = [
                'Company Name', 'Phone Number', 'Email', 'Website / Link',
                'Contact Person', 'Address', 'Category', 'Products',
                'Search Keyword', 'Scraped Date'
            ]

            df = df.reindex(columns=column_order)

            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='D2C Brands', index=False)

                # Auto-adjust column widths
                worksheet = writer.sheets['D2C Brands']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            logger.info(f"Data saved to {filename}")

        except Exception as e:
            logger.error(f"Error saving to Excel: {str(e)}")

    def print_summary(self):
        """Print summary statistics of scraped data"""
        if not self.scraped_data:
            logger.info("No data to summarize")
            return

        logger.info("\n" + "="*50)
        logger.info("SCRAPING SUMMARY")
        logger.info("="*50)
        logger.info(f"Total businesses found: {len(self.scraped_data)}")

        # Category breakdown
        categories = {}
        for business in self.scraped_data:
            category = business.get('Category', 'Unknown')
            categories[category] = categories.get(category, 0) + 1

        logger.info("\nCategory breakdown:")
        for category, count in sorted(categories.items()):
            logger.info(f"  {category}: {count}")

        # Businesses with contact info
        with_phone = sum(1 for b in self.scraped_data if b.get('Phone Number'))
        with_email = sum(1 for b in self.scraped_data if b.get('Email'))
        with_website = sum(1 for b in self.scraped_data if b.get('Website / Link'))

        logger.info(f"\nContact information availability:")
        logger.info(f"  With phone number: {with_phone}")
        logger.info(f"  With email: {with_email}")
        logger.info(f"  With website: {with_website}")
        logger.info("="*50)


def main():
    """Main function to run the scraper"""
    print("JustDial D2C Brand Scraper for Ahmedabad")
    print("="*50)

    # Configuration
    headless = True  # Set to False to see browser window
    max_pages_per_keyword = 2  # Adjust based on needs

    # Initialize scraper
    scraper = JustDialD2CScraper(headless=headless)

    try:
        # Run scraping
        scraper.scrape_all_keywords(max_pages_per_keyword)

        # Remove duplicates
        scraper.remove_duplicates()

        # Save results
        scraper.save_to_csv()
        scraper.save_to_excel()

        print("\nScraping completed successfully!")
        print("Check the generated CSV and Excel files for results.")

    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
    except Exception as e:
        logger.error(f"Scraping failed: {str(e)}")
    finally:
        if scraper.driver:
            scraper.driver.quit()


if __name__ == "__main__":
    main()
