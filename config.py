"""
Configuration file for JustDial D2C Brand Scraper
"""

# Scraping Configuration
SCRAPING_CONFIG = {
    'headless': True,  # Set to False to see browser window
    'max_pages_per_keyword': 2,  # Number of pages to scrape per keyword
    'delay_range': (2, 5),  # Random delay range between requests (seconds)
    'timeout': 15,  # Page load timeout (seconds)
    'inter_keyword_delay': (5, 10),  # Delay between keywords (seconds)
}

# Browser Configuration
BROWSER_CONFIG = {
    'window_size': '1920,1080',
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'disable_images': True,  # Disable image loading for faster scraping
    'disable_css': False,  # Disable CSS loading
}

# Output Configuration
OUTPUT_CONFIG = {
    'csv_filename': None,  # Auto-generate if None
    'excel_filename': None,  # Auto-generate if None
    'include_timestamp': True,
    'auto_adjust_excel_columns': True,
}

# Filtering Configuration
FILTER_CONFIG = {
    'require_ahmedabad_address': True,
    'exclude_keywords': [
        'it service', 'software', 'digital marketing', 'seo', 'web development',
        'trading', 'wholesale', 'distributor', 'dealer', 'agent', 'broker',
        'consultant', 'consultancy', 'agency', 'advertising', 'call center',
        'bpo', 'outsourcing', 'data entry', 'typing', 'xerox', 'photocopy'
    ],
    'minimum_company_name_length': 2,
    'require_phone_or_address': True,
}

# Logging Configuration
LOGGING_CONFIG = {
    'level': 'INFO',  # DEBUG, INFO, WARNING, ERROR
    'log_file': 'justdial_scraper.log',
    'console_output': True,
    'file_output': True,
}

# Additional Keywords (can be customized)
ADDITIONAL_KEYWORDS = [
    # Add more specific keywords here if needed
    "handloom ahmedabad",
    "block printing ahmedabad", 
    "tie dye ahmedabad",
    "organic products ahmedabad",
    "natural products ahmedabad",
    "sustainable brand ahmedabad",
    "zero waste ahmedabad",
    "upcycled products ahmedabad",
    "bamboo products ahmedabad",
    "jute products ahmedabad",
]

# Category Mapping (can be extended)
EXTENDED_CATEGORIES = {
    'handmade': 'Handicrafts',
    'artisan': 'Handicrafts', 
    'craft': 'Handicrafts',
    'pottery': 'Handicrafts',
    'ceramic': 'Handicrafts',
    'handloom': 'Textiles',
    'weaving': 'Textiles',
    'block print': 'Textiles',
    'tie dye': 'Textiles',
    'fashion': 'Apparel',
    'clothing': 'Apparel',
    'garment': 'Apparel',
    'jewelry': 'Accessories',
    'jewellery': 'Accessories',
    'ethnic': 'Apparel',
    'traditional': 'Apparel',
    'decor': 'Home Decor',
    'furniture': 'Home Decor',
    'candle': 'Home Decor',
    'lighting': 'Home Decor',
    'lifestyle': 'Lifestyle',
    'gifts': 'Gifts',
    'gifting': 'Gifts',
    'personalized': 'Gifts',
    'custom': 'Gifts',
    'organic': 'Organic Products',
    'natural': 'Organic Products',
    'sustainable': 'Sustainable Products',
    'eco-friendly': 'Sustainable Products',
    'zero waste': 'Sustainable Products',
    'bamboo': 'Sustainable Products',
    'jute': 'Sustainable Products',
    'food': 'Food & Beverages',
    'snacks': 'Food & Beverages',
    'spices': 'Food & Beverages',
    'tea': 'Food & Beverages',
    'coffee': 'Food & Beverages',
    'cosmetic': 'Beauty & Personal Care',
    'skincare': 'Beauty & Personal Care',
    'beauty': 'Beauty & Personal Care',
    'soap': 'Beauty & Personal Care',
    'art': 'Art & Crafts',
    'painting': 'Art & Crafts',
    'sculpture': 'Art & Crafts',
    'design': 'Design Services',
    'studio': 'Design Services',
}
