#!/usr/bin/env python3
"""
Example usage of JustDial D2C Brand Scraper
===========================================

This script demonstrates different ways to use the scraper with custom configurations.
"""

from justdial_d2c_scraper import JustDialD2CScraper
import logging

def example_basic_usage():
    """Basic usage example with default settings"""
    print("Example 1: Basic Usage")
    print("-" * 30)
    
    # Initialize scraper with default settings
    scraper = JustDialD2CScraper()
    
    # Run scraping for all keywords (2 pages each)
    scraper.scrape_all_keywords(max_pages_per_keyword=2)
    
    # Remove duplicates
    scraper.remove_duplicates()
    
    # Save results
    scraper.save_to_csv()
    scraper.save_to_excel()
    
    print("Basic scraping completed!")

def example_custom_keywords():
    """Example with custom keywords"""
    print("\nExample 2: Custom Keywords")
    print("-" * 30)
    
    # Initialize scraper
    scraper = JustDialD2CScraper(headless=True, delay_range=(3, 6))
    
    # Override with custom keywords
    scraper.keywords = [
        "handmade jewelry ahmedabad",
        "organic food ahmedabad", 
        "sustainable fashion ahmedabad",
        "artisan pottery ahmedabad",
        "custom gifts ahmedabad"
    ]
    
    scraper.setup_driver()
    
    # Scrape each keyword
    for keyword in scraper.keywords:
        businesses = scraper.scrape_keyword(keyword, max_pages=1)
        scraper.scraped_data.extend(businesses)
    
    # Clean up
    scraper.driver.quit()
    scraper.remove_duplicates()
    
    # Save with custom filename
    scraper.save_to_csv("custom_keywords_results.csv")
    
    print(f"Custom keyword scraping completed! Found {len(scraper.scraped_data)} businesses")

def example_single_keyword():
    """Example scraping a single keyword with detailed control"""
    print("\nExample 3: Single Keyword Detailed Scraping")
    print("-" * 45)
    
    # Initialize scraper (non-headless to see browser)
    scraper = JustDialD2CScraper(headless=False, delay_range=(2, 4))
    scraper.setup_driver()
    
    try:
        # Scrape single keyword with more pages
        keyword = "handcrafted gifts ahmedabad"
        businesses = scraper.scrape_keyword(keyword, max_pages=5)
        
        print(f"\nFound {len(businesses)} businesses for '{keyword}':")
        
        # Display first few results
        for i, business in enumerate(businesses[:3], 1):
            print(f"\n{i}. {business.get('Company Name', 'N/A')}")
            print(f"   Phone: {business.get('Phone Number', 'N/A')}")
            print(f"   Address: {business.get('Address', 'N/A')[:50]}...")
            print(f"   Category: {business.get('Category', 'N/A')}")
        
        # Save results
        scraper.scraped_data = businesses
        scraper.save_to_csv(f"single_keyword_{keyword.replace(' ', '_')}.csv")
        
    finally:
        scraper.driver.quit()

def example_filtered_scraping():
    """Example with custom filtering"""
    print("\nExample 4: Filtered Scraping")
    print("-" * 30)
    
    class FilteredScraper(JustDialD2CScraper):
        def is_valid_d2c_business(self, business_info):
            """Custom filtering logic"""
            # Call parent validation first
            if not super().is_valid_d2c_business(business_info):
                return False
            
            # Additional custom filters
            company_name = business_info.get('Company Name', '').lower()
            
            # Must have certain keywords
            required_keywords = ['handmade', 'artisan', 'craft', 'design', 'studio', 'creative']
            has_required = any(keyword in company_name for keyword in required_keywords)
            
            # Must have phone number
            has_phone = bool(business_info.get('Phone Number'))
            
            return has_required and has_phone
    
    # Use custom scraper
    scraper = FilteredScraper()
    
    # Focus on specific categories
    scraper.keywords = [
        "handmade products ahmedabad",
        "artisan brand ahmedabad",
        "craft studio ahmedabad",
        "design studio ahmedabad"
    ]
    
    scraper.scrape_all_keywords(max_pages_per_keyword=2)
    scraper.remove_duplicates()
    scraper.save_to_csv("filtered_results.csv")
    
    print(f"Filtered scraping completed! Found {len(scraper.scraped_data)} qualifying businesses")

def example_category_analysis():
    """Example with category analysis"""
    print("\nExample 5: Category Analysis")
    print("-" * 30)
    
    # Run basic scraping
    scraper = JustDialD2CScraper()
    scraper.scrape_all_keywords(max_pages_per_keyword=1)
    scraper.remove_duplicates()
    
    # Analyze categories
    categories = {}
    for business in scraper.scraped_data:
        category = business.get('Category', 'Unknown')
        if category not in categories:
            categories[category] = []
        categories[category].append(business)
    
    # Print analysis
    print(f"\nCategory Analysis ({len(scraper.scraped_data)} total businesses):")
    print("-" * 50)
    
    for category, businesses in sorted(categories.items()):
        print(f"\n{category}: {len(businesses)} businesses")
        
        # Show top 2 businesses in each category
        for i, business in enumerate(businesses[:2], 1):
            print(f"  {i}. {business.get('Company Name', 'N/A')}")
    
    # Save category-wise files
    for category, businesses in categories.items():
        if len(businesses) > 0:
            filename = f"category_{category.lower().replace(' ', '_')}.csv"
            
            # Create temporary scraper instance for saving
            temp_scraper = JustDialD2CScraper()
            temp_scraper.scraped_data = businesses
            temp_scraper.save_to_csv(filename)

def main():
    """Run all examples"""
    print("JustDial D2C Brand Scraper - Usage Examples")
    print("=" * 50)
    
    # Set logging level for examples
    logging.getLogger().setLevel(logging.WARNING)
    
    try:
        # Run examples (comment out the ones you don't want to run)
        
        # Example 1: Basic usage
        # example_basic_usage()
        
        # Example 2: Custom keywords  
        example_custom_keywords()
        
        # Example 3: Single keyword (opens browser window)
        # example_single_keyword()
        
        # Example 4: Filtered scraping
        # example_filtered_scraping()
        
        # Example 5: Category analysis
        # example_category_analysis()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        print("Check the generated files for results.")
        
    except KeyboardInterrupt:
        print("\nExamples interrupted by user")
    except Exception as e:
        print(f"\nError running examples: {str(e)}")

if __name__ == "__main__":
    main()
